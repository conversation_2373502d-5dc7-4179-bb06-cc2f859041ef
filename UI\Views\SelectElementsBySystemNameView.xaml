﻿<UserControl
    x:Class="MEP.DuctTools.UI.Views.SelectElementsBySystemNameView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:colView="system.windows.da"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.DuctTools.UI.Views"
    xmlns:mah="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:models="clr-namespace:MEP.DuctTools.UI.ViewModels"
    xmlns:system="clr-namespace:System;assembly=mscorlib"
    mc:Ignorable="d">


    <UserControl.Resources>
        <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
    </UserControl.Resources>

    <StackPanel>

        <materialDesign:ColorZone
            Width="300"
            Margin="20"
            Padding="8,4,8,4"
            HorizontalAlignment="Left"
            Panel.ZIndex="1"
            materialDesign:ElevationAssist.Elevation="Dp2"
            CornerRadius="2"
            Mode="Standard">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="Auto" />
                </Grid.ColumnDefinitions>
                <Button Style="{StaticResource MaterialDesignToolButton}">
                    <materialDesign:PackIcon Kind="Search" Opacity=".56" />
                </Button>
                <TextBox
                    Grid.Column="1"
                    MinWidth="200"
                    Margin="8,0,0,0"
                    VerticalAlignment="Center"
                    materialDesign:HintAssist.Hint="Search ..."
                    materialDesign:TextFieldAssist.DecorationVisibility="Hidden"
                    BorderThickness="0"
                    Text="{Binding FilterTxt, UpdateSourceTrigger=PropertyChanged}" />

            </Grid>
        </materialDesign:ColorZone>


        <ScrollViewer
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch"
            materialDesign:ScrollViewerAssist.IsAutoHideEnabled="True"
            VerticalScrollBarVisibility="Auto">

            <TreeView
                MinWidth="220"
                MinHeight="220"
                MaxHeight="600"
                Margin="20"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Top"
                ItemsSource="{Binding RvtMechincalSystems}">
                <TreeView.Resources>

                    <Style BasedOn="{StaticResource MaterialDesignTreeViewItem}" TargetType="TreeViewItem">
                        <Setter Property="materialDesign:TreeViewAssist.ExpanderSize" Value="15" />
                        <Setter Property="materialDesign:TreeViewAssist.ShowSelection" Value="False" />
                    </Style>


                    <HierarchicalDataTemplate DataType="{x:Type models:SelectableGroupedMechincalSystemViewModel}" ItemsSource="{Binding MechanicalSystemsView}">
                        <StackPanel Orientation="Horizontal">
                            <ToggleButton
                                Width="15"
                                Height="15"
                                Margin="0,0,5,0"
                                materialDesign:ToggleButtonAssist.OnContent="{materialDesign:PackIcon Kind=CheckBold,
                                                                                                      Size=15}"
                                Background="{StaticResource BecaTealBrush}"
                                Content="{materialDesign:PackIcon Kind=CloseThick,
                                                                  Size=15}"
                                Foreground="White"
                                IsChecked="{Binding IsSelected}"
                                Style="{StaticResource MaterialDesignActionToggleButton}" />
                            <TextBlock
                                VerticalAlignment="Center"
                                FontSize="16"
                                FontWeight="Bold"
                                Text="{Binding ViewModel.GroupName}" />

                        </StackPanel>
                    </HierarchicalDataTemplate>


                    <DataTemplate DataType="{x:Type models:SelectableMechanicalSystemViewModel}">

                        <StackPanel Orientation="Horizontal">

                            <ToggleButton
                                Width="15"
                                Height="15"
                                Margin="0,0,5,0"
                                materialDesign:ToggleButtonAssist.OnContent="{materialDesign:PackIcon Kind=CheckBold,
                                                                                                      Size=15}"
                                Background="{StaticResource BecaTealBrush}"
                                Content="{materialDesign:PackIcon Kind=CloseThick,
                                                                  Size=15}"
                                Foreground="White"
                                IsChecked="{Binding IsSelected}"
                                Style="{StaticResource MaterialDesignActionToggleButton}" />

                            <TextBlock
                                Margin="3,2"
                                VerticalAlignment="Center"
                                FontWeight="Bold"
                                Text="{Binding ViewModel.Name}" />

                        </StackPanel>

                    </DataTemplate>
                </TreeView.Resources>
            </TreeView>

        </ScrollViewer>

        <!--<TreeView
            Grid.Row="0"
            MinWidth="220"
            MinHeight="250"
            MaxWidth="600"
            MaxHeight="600"
            Margin="20"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Top"
            d:Height="300"
            ItemsSource="{Binding RvtMechincalSystems}">
            <TreeView.Resources>

                <Style BasedOn="{StaticResource MaterialDesignTreeViewItem}" TargetType="TreeViewItem">
                    <Setter Property="materialDesign:TreeViewAssist.ExpanderSize" Value="15" />
                    <Setter Property="materialDesign:TreeViewAssist.ShowSelection" Value="False" />
                </Style>


                <HierarchicalDataTemplate DataType="{x:Type models:SelectableGroupedMechincalSystemViewModel}" ItemsSource="{Binding ViewModel.MechanicalSystems}">
                    <StackPanel Orientation="Horizontal">
                        <ToggleButton
                            Width="15"
                            Height="15"
                            Margin="0,0,5,0"
                            materialDesign:ToggleButtonAssist.OnContent="{materialDesign:PackIcon Kind=CheckBold,
                                                                                                  Size=15}"
                            Background="{StaticResource BecaTealBrush}"
                            Content="{materialDesign:PackIcon Kind=CloseThick,
                                                              Size=15}"
                            Foreground="White"
                            IsChecked="{Binding IsSelected}"
                            Style="{StaticResource MaterialDesignActionToggleButton}" />
                        <TextBlock
                            VerticalAlignment="Center"
                            FontSize="16"
                            FontWeight="Bold"
                            Text="{Binding ViewModel.GroupName}" />

                    </StackPanel>
                </HierarchicalDataTemplate>


                <DataTemplate DataType="{x:Type models:SelectableMechanicalSystemViewModel}">

                    <StackPanel Orientation="Horizontal">

                        <ToggleButton
                            Width="15"
                            Height="15"
                            Margin="0,0,5,0"
                            materialDesign:ToggleButtonAssist.OnContent="{materialDesign:PackIcon Kind=CheckBold,
                                                                                                  Size=15}"
                            Background="{StaticResource BecaTealBrush}"
                            Content="{materialDesign:PackIcon Kind=CloseThick,
                                                              Size=15}"
                            Foreground="White"
                            IsChecked="{Binding IsSelected}"
                            Style="{StaticResource MaterialDesignActionToggleButton}" />

                        <TextBlock
                            Margin="3,2"
                            VerticalAlignment="Center"
                            FontWeight="Bold"
                            Text="{Binding ViewModel.Name}" />


                    </StackPanel>

                </DataTemplate>

            </TreeView.Resources>
        </TreeView>-->

        <StackPanel
            Grid.Row="4"
            HorizontalAlignment="Center"
            Orientation="Horizontal">

            <Button
                Margin="5"
                Background="{StaticResource BecaTealBrush}"
                BorderBrush="{StaticResource BecaTealBrush}"
                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                Foreground="White"
                IsDefault="True"
                Style="{StaticResource MaterialDesignRaisedLightButton}">
                <Button.CommandParameter>
                    <system:Boolean>True</system:Boolean>
                </Button.CommandParameter>
                ACCEPT
            </Button>

            <Button
                Margin="5"
                Background="{StaticResource BecaFuschiaBrush}"
                BorderBrush="{StaticResource BecaFuschiaBrush}"
                Command="{x:Static materialDesign:DialogHost.CloseDialogCommand}"
                Foreground="White"
                IsCancel="True"
                Style="{StaticResource MaterialDesignRaisedLightButton}">
                <Button.CommandParameter>
                    <system:Boolean>False</system:Boolean>
                </Button.CommandParameter>
                CANCEL
            </Button>

        </StackPanel>

    </StackPanel>
</UserControl>
