﻿using Autodesk.Revit.DB;
using Common.UI.WPF.UI.ViewModels;
using MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Data;

namespace MEP.DuctTools.UI.ViewModels
{
    class SelectableGroupedMechincalSystemViewModel : SelectableViewModel<GroupedMechincalSystemViewModel>
    {
        ListCollectionView _mechanicalSystemsView;

        public ListCollectionView MechanicalSystemsView { get => _mechanicalSystemsView; set => _mechanicalSystemsView = value; }


        public SelectableGroupedMechincalSystemViewModel(GroupedMechincalSystemViewModel obj) : base(obj)
        {
            MechanicalSystemsView = new ListCollectionView(obj.MechanicalSystems);
        }

        public override void IsSelectedChanged(bool value)
        {
            foreach (SelectableMechanicalSystemViewModel item in MechanicalSystemsView)
            {
                item.IsSelected = value;
            }
        }
    }


    class SelectableMechanicalSystemViewModel : SelectableViewModel<MechanicalSystemViewModel>
    {
        public SelectableMechanicalSystemViewModel(MechanicalSystemViewModel obj) : base(obj)
        {

        }
    }
}