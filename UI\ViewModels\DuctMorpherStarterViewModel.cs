using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities.Collectors;
using Common.UI.WPF.UI.ViewModels;
using Common.UI.WPF.UI.Views;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ControlzEx.Standard;
using Egor92.MvvmNavigation;
using Egor92.MvvmNavigation.Abstractions;
using MaterialDesignThemes.Wpf;
using MEP.DuctTools.CoreLogic;
using MEP.DuctTools.UI.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using UIFramework;
using Common.Utilities;
using System.Windows.Data;
using System.Collections.ObjectModel;
using MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels;
using System.ComponentModel;
using Autodesk.Revit.DB.Mechanical;
using System.Windows.Forms;
using MEP.DuctTools.UI.Views;
using Autodesk.Revit.UI.Selection;
using BecaRevitUtilities.SelectionFilters;
using CommunityToolkit.Mvvm.DependencyInjection;
using System.Drawing.Drawing2D;

namespace MEP.DuctTools.UI.ViewModels
{
    internal partial class DuctMorpherStarterViewModel : ObservableObject
    {

        #region Fields

        UIDocument _uiRvtDoc;

        NavigationManager _navigationManager;
        Action _closeForm;

        Action _hideForm;
        Action _showForm;

        BecaActivityLoggerData _taskLogger;

        // Static field to temporarily store the process for navigation
        internal static DuctMorpherProcess CurrentProcess { get; set; }




        [ObservableProperty]
        ElementSelectorMethods _elementSelectorMethod;

        Dictionary<string, ElementSelectorMethods> _elementsSelectorMethods;


        [ObservableProperty]
        ObservableCollection<BaseDuctElementViewModel> _selectedElements;

        [ObservableProperty]
        ListCollectionView _selectedElementsView;

        [ObservableProperty]
        bool _isForcingFittingChanges;

        List<DuctType> _rvtRectangleDuctTypes;
        [ObservableProperty]
        DuctType _selectedRectangleDuctType;


        List<DuctType> _rvtOvalDuctTypes;
        [ObservableProperty]
        DuctType _selectedOvalDuctType;


        List<DuctType> _rvtRoundDuctTypes;
        [ObservableProperty]
        DuctType _selectedRoundDuctType;
        #endregion

        #region Properties

        public Dictionary<string, ElementSelectorMethods> ElementsSelectorMethods { get => _elementsSelectorMethods; }
        public List<DuctType> RvtRectangleDuctTypes { get => _rvtRectangleDuctTypes; set => _rvtRectangleDuctTypes = value; }
        public List<DuctType> RvtOvalDuctTypes { get => _rvtOvalDuctTypes; set => _rvtOvalDuctTypes = value; }
        public List<DuctType> RvtRoundDuctTypes { get => _rvtRoundDuctTypes; set => _rvtRoundDuctTypes = value; }

        #endregion

        #region Constructors

        public DuctMorpherStarterViewModel(UIDocument uiRvtDoc, NavigationManager navigationManager, BecaActivityLoggerData taskLogger, Action closeForm, Action hideForm, Action showForm)
        {
            _navigationManager = navigationManager;
            _closeForm = closeForm;
            _hideForm = hideForm;
            _showForm = showForm;
            _uiRvtDoc = uiRvtDoc;
            _taskLogger = taskLogger;

            _elementsSelectorMethods = EnumExtensions.ToHumanReadableDictionary<ElementSelectorMethods>();
            _selectedElements = new ObservableCollection<BaseDuctElementViewModel>();
            _selectedElementsView = new ListCollectionView(_selectedElements);
            _selectedElementsView.GroupDescriptions.Add(new PropertyGroupDescription() { PropertyName = nameof(BaseDuctElementViewModel.Category) });
            _selectedElementsView.SortDescriptions.Add(new SortDescription(nameof(BaseDuctElementViewModel.ElementId), ListSortDirection.Ascending));

            var rvtDuctTypes = new FilteredElementCollector(_uiRvtDoc.Document).OfClass(typeof(DuctType)).Cast<DuctType>().ToList();

            RvtRectangleDuctTypes = rvtDuctTypes.Where(x => x.Shape == ConnectorProfileType.Rectangular).ToList();
            RvtOvalDuctTypes = rvtDuctTypes.Where(x => x.Shape == ConnectorProfileType.Oval).ToList();
            RvtRoundDuctTypes = rvtDuctTypes.Where(x => x.Shape == ConnectorProfileType.Round).ToList();


            _selectedRectangleDuctType = RvtRectangleDuctTypes.FirstOrDefault();
            _selectedOvalDuctType = RvtOvalDuctTypes.FirstOrDefault();
            _selectedRoundDuctType = RvtRoundDuctTypes.FirstOrDefault();

            _isForcingFittingChanges = true;
        }


        #endregion

        #region Methods

        #region Partial Methods


        #endregion


        #region Helpers

        private void SelectElementsByRvtElementsSelect(bool isAppending)
        {
            try
            {

                IList<Reference> refs = _uiRvtDoc.Selection.PickObjects(ObjectType.Element, new CategoriesBasedSelectionFilter(new List<BuiltInCategory>(2) { BuiltInCategory.OST_DuctFitting, BuiltInCategory.OST_DuctCurves }), "Please select elements");
                if (refs != null)
                {
                    if (!isAppending)
                    {
                        SelectedElements.Clear();
                    }
                    foreach (Element ele in refs.Select(eleID => _uiRvtDoc.Document.GetElement(eleID)))
                    {
                        if (ele is Duct)
                        {
                            SelectedElements.Add(new DuctElementViewModel(ele as Duct));
                        }
                        else
                        {
                            SelectedElements.Add(new DuctFittingElementViewModel(ele as FamilyInstance));
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                _taskLogger.Log(ex.Message, LogType.Error);
            }
        }

        private void SelectElementsByRvtSelectedElements(bool isAppending)
        {
            try
            {
                var selectedElements = _uiRvtDoc.Selection.GetElementIds().Select(id => _uiRvtDoc.Document.GetElement(id));
                if (selectedElements != null)
                {
                    if (!isAppending)
                    {
                        SelectedElements.Clear();
                    }
                    foreach (Element ele in selectedElements)
                    {
                        if (ele is Duct)
                        {
                            SelectedElements.Add(new DuctElementViewModel(ele as Duct));
                        }
                        else
                        {
                            SelectedElements.Add(new DuctFittingElementViewModel(ele as FamilyInstance));
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                _taskLogger.Log(ex.Message, LogType.Error);
            }
        }

        private void SelectElementsByRvtSystemSelect(bool isAppending)
        {
            try
            {
                var reference = _uiRvtDoc.Selection.PickObject(ObjectType.Element,
                    new CategoriesBasedSelectionFilter(new List<BuiltInCategory>(2) { BuiltInCategory.OST_DuctFitting, BuiltInCategory.OST_DuctCurves }), "Please select a duct or fitting from a system");
                if (reference != null)
                {
                    var el = _uiRvtDoc.Document.GetElement(reference.ElementId);
                    if (!isAppending)
                    {
                        SelectedElements.Clear();
                    }
                    AddElementsBySystem(el.get_Parameter(BuiltInParameter.RBS_SYSTEM_NAME_PARAM)?.AsString());
                }

            }
            catch (Exception ex)
            {
                _taskLogger.Log(ex.Message, LogType.Error);
            }
        }

        private async Task SelectElementsBySystemName(bool isAppending)
        {
            var vm = new SelectElementsBySystemNameViewModel(_uiRvtDoc.Document, _navigationManager, _taskLogger);
            var view = new SelectElementsBySystemNameView
            {
                DataContext = vm
            };

            var result = await DialogHost.Show(view, "RootDialog_DM", null, null, null);
            if ((bool)result == true)
            {
                if (!isAppending)
                {
                    SelectedElements.Clear();
                }

                foreach (SelectableGroupedMechincalSystemViewModel RvtMechincalSystemsGrp in vm.RvtMechincalSystems)
                {
                    foreach (SelectableMechanicalSystemViewModel item in RvtMechincalSystemsGrp.ViewModel.MechanicalSystems.Where(x => x.IsSelected).ToList())
                    {
                        AddElementsBySystem(item.ViewModel.Name);
                    }
                }
            }
        }

        void AddElementsBySystem(string systemName)
        {
            if (systemName == null)
            {
                systemName = string.Empty;
            }

            var pmFilter = ParameterFilterRuleFactory.CreateEqualsRule(new ElementId((int)BuiltInParameter.RBS_SYSTEM_NAME_PARAM), systemName, false);

            ElementParameterFilter paramFilter = new ElementParameterFilter(pmFilter);

            var ducts = new FilteredElementCollector(_uiRvtDoc.Document).WhereElementIsNotElementType().OfCategory(BuiltInCategory.OST_DuctCurves).WherePasses(paramFilter).Cast<Duct>().ToList();

            foreach (Duct duct in ducts)
            {
                SelectedElements.Add(new DuctElementViewModel(duct));
            }
            var ductsFittings = new FilteredElementCollector(_uiRvtDoc.Document).WhereElementIsNotElementType().OfCategory(BuiltInCategory.OST_DuctFitting).WherePasses(paramFilter).Cast<FamilyInstance>().ToList();
            foreach (FamilyInstance ductFittings in ductsFittings)
            {
                SelectedElements.Add(new DuctFittingElementViewModel(ductFittings));
            }
        }

        #endregion

        #region Rely Commands

        [RelayCommand]
        public void Exit()
        {
            _closeForm();
        }

        [RelayCommand]
        public void Run()
        {
            if (SelectedElements.Any())
            {
                DuctMorpherProcess process = new DuctMorpherProcess(SelectedElements.ToList(), _selectedRoundDuctType, _selectedOvalDuctType, _selectedRectangleDuctType, _isForcingFittingChanges);

                // Store the process in the static field for the ExecutingLogicViewModel to retrieve
                CurrentProcess = process;

                _navigationManager.Navigate(nameof(ExecutingLogicView));
            }
            else
            {
                MessageBox.Show("Please select systems or elements ...");
            }
        }

        [RelayCommand]
        public async Task SelectAsync()
        {
            switch (_elementSelectorMethod)
            {
                case ElementSelectorMethods.BySystemName:
#if TargetYear2025
                    MessageBox.Show("This feature is currently not available for Revit 2025", "Info", MessageBoxButtons.OK, MessageBoxIcon.Information);
#else
                    await SelectElementsBySystemName(false);
#endif
                    break;
                case ElementSelectorMethods.BySelectingSystem:
                    _hideForm();
                    SelectElementsByRvtSystemSelect(false);
                    _showForm();
                    break;

                case ElementSelectorMethods.BySelectingElement:
                    _hideForm();
                    SelectElementsByRvtElementsSelect(false);
                    _showForm();
                    break;

                case ElementSelectorMethods.BySelectedElement:
                    _hideForm();
                    SelectElementsByRvtSelectedElements(false);
                    _showForm();
                    break;
            }
        }


        [RelayCommand]
        public async Task AppendAsync()
        {
            switch (_elementSelectorMethod)
            {
                case ElementSelectorMethods.BySystemName:
                    await SelectElementsBySystemName(true);
                    break;
                case ElementSelectorMethods.BySelectingSystem:
                    _hideForm();
                    SelectElementsByRvtSystemSelect(true);
                    _showForm();
                    break;

                case ElementSelectorMethods.BySelectingElement:
                    _hideForm();
                    SelectElementsByRvtElementsSelect(true);
                    _showForm();
                    break;
            }
        }

        #endregion

        #endregion

    }
}