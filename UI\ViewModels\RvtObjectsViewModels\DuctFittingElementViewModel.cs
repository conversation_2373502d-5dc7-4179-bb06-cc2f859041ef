﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using BecaRevitUtilities.ElementUtilities;
using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels
{
    [ObservableObject]
    internal partial class DuctFittingElementViewModel : BaseDuctElementViewModel
    {

        #region Fields



        #endregion

        #region Properties



        #endregion

        #region Constructors

        public DuctFittingElementViewModel(FamilyInstance rvtElement) : base(rvtElement)
        {
            _ductShape = ConnectorProfileType.Invalid;
            _size = rvtElement.get_Parameter(BuiltInParameter.RBS_CALCULATED_SIZE)?.AsString();
        }

        #endregion

        #region Methods

        public override bool ChangeType(DuctType newType)
        {
            return true;
        }

        #endregion

    }
}