﻿using Autodesk.Revit.Attributes;
using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaCommand;
using Egor92.MvvmNavigation;
using Egor92.MvvmNavigation.Abstractions;
using GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers;
using MEP.DuctTools.UI.ViewModels;
using MEP.DuctTools.UI.Views;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace MEP.DuctTools.RevitCommands
{
    [Transaction(TransactionMode.Manual)]
    internal class DuctMorpherCommand : BecaBaseCommand
    {

        public override Result ExecuteBecaCommand(ExternalCommandData commandData, ref string message, Autodesk.Revit.DB.ElementSet elements)
        {
            UIApplication uiapp = commandData.Application;
            UIDocument uidoc = uiapp.ActiveUIDocument;
            var app = uiapp.Application;
            Document doc = uidoc.Document;

            _taskLogger.PreTaskStart();

            #region Core Logic

            DuctMorpherMainView.RunForm(uidoc, _taskLogger);

            #endregion

            _taskLogger.PostTaskEnd("Duct Morpher : Check Detailed Logs.");

            return Result.Succeeded;
        }


        public override string GetAddinAuthor()
        {
            return "Ameer Mansour";
        }

        public override string GetAddinName()
        {
            return AddinNames.DuctTools.Value;
        }

        public override string GetCommandSubName()
        {
            return "Duct Morpher";
        }
    }
}
