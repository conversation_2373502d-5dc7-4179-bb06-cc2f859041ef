﻿using Autodesk.Revit.DB.Plumbing;
using Autodesk.Revit.DB;
using BecaRevitUtilities.Collectors;
using BecaTransactionsNamesManager;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Autodesk.Revit.DB.Structure;
using Autodesk.Revit.DB.Mechanical;
using BecaActivityLogger.CoreLogic.Data;
using Microsoft.Office.Interop.Excel;
using BecaRevitUtilities.RevitViewsUtilities;
using Autodesk.Revit.UI;
using BecaRevitUtilities;
using CommunityToolkit.Mvvm.DependencyInjection;
using MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels;
using MEP.DuctTools.UI.Models;
using BecaRevitUtilities.FailureHandlers;
using BecaRevitUtilities.MEPUtilities.MEPElementsUtilities;
using GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers.RevitRequests;

namespace MEP.DuctTools.CoreLogic
{
    internal class DuctMorpherMainLogic
    {

        #region Fields

        Action<string, LogType> _log;
        Action<int> _reportProgress;
        UIDocument _uiRvtDoc;
        DuctMorpherProcess _process;


        #endregion

        #region Properties



        #endregion

        #region Constructors

        public DuctMorpherMainLogic(UIDocument uiRvtDoc, DuctMorpherProcess process, Action<string, LogType> log, Action<int> reportProgress)
        {
            _process = process;
            _log = log;
            _reportProgress = reportProgress;
            _uiRvtDoc = uiRvtDoc;
        }

        #endregion

        #region Methods

        internal bool Run()
        {
            using (TransactionGroup txGroup = new TransactionGroup(_uiRvtDoc.Document, "Duct Morpher"))
            {
                txGroup.Start();


                if (!Mapping())
                {
                    _reportProgress(0);
                    _log("ERROR : FAILED. CAN NOT DO MAPPING !", LogType.Error);
                    txGroup.RollBack();
                    return false;
                }

                txGroup.Assimilate();
                _reportProgress(100);
            }

            return true;
        }

        private bool Mapping()
        {
            var fittingsToChangeType = new List<FittingTypeChanger>();

            using (Transaction tx = new Transaction(_uiRvtDoc.Document, BecaTransactionsNames.DuctMorpher.GetHumanReadableString()))
            {

                if (_process.IsForcingFittingChanges)
                {
                    var fittingFailuresPreprocessor = new GeneralWarningFailuresHandler((s) => LogFailures(s));
                    var failureOptions = tx.GetFailureHandlingOptions();
                    failureOptions.SetFailuresPreprocessor(fittingFailuresPreprocessor);
                    failureOptions.SetClearAfterRollback(true);
                    tx.SetFailureHandlingOptions(failureOptions);
                }

                tx.Start();

                _log("          Mapping Ducts...", LogType.Information);
                var ductsVms = _process.ElementsToProcess.Where(x => x is DuctElementViewModel && !x.IsLocked).Cast<DuctElementViewModel>();
                var ductsCount = ductsVms.Count();
                var stageMaxPrecentage = 85;//mapping is 85% from the whole process. 


                _log($"Processing {ductsCount} duct ...", LogType.Information);

                for (int i = 0; i < ductsCount; i++)
                {
                    var mapper = ductsVms.ElementAt(i);
                    mapper.ChangeType(_process.GetDuctType(mapper.DuctShape));
                    fittingsToChangeType.AddRange(mapper.FittingsToChangeType);
                    _reportProgress((i * stageMaxPrecentage) / ductsCount);
                }




                if (tx.Commit() != TransactionStatus.Committed)
                {
                    return false;
                }
                else
                {
                    _log("          Mapping Ducts Ended...", LogType.Information);
                    _reportProgress(stageMaxPrecentage);
                }
            }

            _log("          Changing Ducts fittings...", LogType.Information);


            foreach (DuctFittingElementViewModel item in _process.ElementsToProcess.Where(x => x is DuctFittingElementViewModel && !x.IsLocked && x.RvtElement.IsValidObject))
            {
                var mecMepModel = (item.RvtElement as FamilyInstance).MEPModel as MechanicalFitting;
                Connector selConnector = mecMepModel.ConnectorManager.Connectors.Cast<Connector>().FirstOrDefault();

                double size = 0;
                switch (selConnector.Shape)
                {
                    case ConnectorProfileType.Round:
                        size = 2 * selConnector.Radius;
                        break;
                    case ConnectorProfileType.Rectangular:
                    case ConnectorProfileType.Oval:
                        size = selConnector.Width;
                        break;
                    case ConnectorProfileType.Invalid:
                    default:
                        break;
                }

                var newTypeFitting = MEPElementUtility.GetSuitableFitting(_process.GetDuctType(selConnector.Shape), mecMepModel.PartType, size);
                if (newTypeFitting.IntegerValue == -1)
                {

                }
                if (item.RvtElement.Id.IntegerValue != newTypeFitting.IntegerValue)
                {
                    if (!fittingsToChangeType.Any(x => x.Fitting.Id.IntegerValue == item.RvtElement.Id.IntegerValue))
                    {
                        fittingsToChangeType.Add(new FittingTypeChanger(item.RvtElement, newTypeFitting));
                    }
                }
            }

            List<ElementId> failedElementIds = new List<ElementId>();
            foreach (var mapper in fittingsToChangeType.Where(x => x.Fitting.IsValidObject))
            {
                using (Transaction tx = new Transaction(_uiRvtDoc.Document, BecaTransactionsNames.MappingPipeTypes.GetHumanReadableString()))
                {
                    if (_process.IsForcingFittingChanges)
                    {
                        var fittingFailuresPreprocessor = new GeneralWarningFailuresHandler((s) => LogFailures(s));
                        var failureOptions = tx.GetFailureHandlingOptions();
                        failureOptions.SetFailuresPreprocessor(fittingFailuresPreprocessor);
                        failureOptions.SetClearAfterRollback(true);
                        tx.SetFailureHandlingOptions(failureOptions);
                    }

                    tx.Start();
                    if (!mapper.Run())
                    {
                        _log($"Error || Can not change Duct Fitting {mapper.Fitting.Id.ToString()} to type id {mapper.NewTypeFittingId}", LogType.Error);
                    }

                    if (tx.Commit() != TransactionStatus.Committed)
                    {
                        failedElementIds.Add(mapper.Fitting.Id);
                        _log($"Error || Can not change Duct Fitting {mapper.Fitting.Id.ToString()} to type id {mapper.NewTypeFittingId}", LogType.Error);
                    }
                    else
                    {
                        _log($"Information || Duct Fitting {mapper.Fitting.Id.ToString()} was changed to type id {mapper.NewTypeFittingId}", LogType.Information);
                    }
                }
            }

            if (_process.IsForcingFittingChanges && failedElementIds.Any())
            {
                OverrideGraphicSettings ogs_ElementWithColors, ogs_ElementWithoutColors;
                GraphicOverrideUtility.GetOverrideGraphicSettings(_uiRvtDoc.Document, out ogs_ElementWithoutColors, out ogs_ElementWithColors);


                using (Transaction tx = new Transaction(_uiRvtDoc.Document, BecaTransactionsNames.MappingPipeTypes.GetHumanReadableString()))
                {
                    tx.Start();

                    var failedFittingsView = ThreeDViewUtility.CreateOrGet(_uiRvtDoc.Document, "Ducts Morpher_Failed Fittings_" + _uiRvtDoc.Document.Application.Username);
                    failedFittingsView.IsolateCategoriesTemporary(new List<ElementId>(3) { new ElementId(BuiltInCategory.OST_DuctAccessory), new ElementId(BuiltInCategory.OST_DuctCurves), new ElementId(BuiltInCategory.OST_DuctFitting) });
                    var allElementsInView = new FilteredElementCollector(_uiRvtDoc.Document, failedFittingsView.Id);
                    FilteredElementIdIterator allElementsIDsInView = allElementsInView.GetElementIdIterator();
                    allElementsIDsInView.Reset();
                    while (allElementsIDsInView.MoveNext())
                    {
                        if (failedElementIds.Contains(allElementsIDsInView.Current))
                            failedFittingsView.SetElementOverrides(allElementsIDsInView.Current, ogs_ElementWithColors);
                        else
                            failedFittingsView.SetElementOverrides(allElementsIDsInView.Current, ogs_ElementWithoutColors);// set it to default graphic settings.
                    }

                    if (tx.Commit() != TransactionStatus.Committed)
                    {
                        _log($"Error || Can not create Failed fittings View", LogType.Error);
                    }
                    else
                    {
                        _reportProgress(90);
                        _log($"Information || Failed fittings View was created by name : {failedFittingsView.Name} ", LogType.Information);
                        _uiRvtDoc.ActiveView = failedFittingsView;
                    }
                }

            }

            _reportProgress(95);

            _log("          Changing Duct fittings Ended...", LogType.Information);
            return true;
        }

        void LogFailures(string s)
        {
            if (s.StartsWith("E"))
            {
                _log(s, LogType.Error);
            }
            else
            {
                _log(s, LogType.Warning);
            }
        }

        #endregion

    }
}