﻿using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers
{
    internal abstract class RevitRequest
    {

        #region Fields

        RevitRequestId _revitRequestId;


        #endregion

        #region Properties

        public delegate void OnRequestExcuted(RevitRequest request, bool isSuccessfulExcution);
        public event OnRequestExcuted onRequestExcuted;
        public RevitRequestId RevitRequestId { get => _revitRequestId; }

        #endregion

        #region Constructors

        public RevitRequest(RevitRequestId revitRequestId)
        {
            _revitRequestId = revitRequestId;
        }

        #endregion

        #region Methods

        protected abstract bool Execute(UIApplication uiapp, BecaActivityLoggerData logger);

        public void ExcuteRequest(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            var result = Execute(uiapp, logger);
            if (onRequestExcuted != null)
            {
                onRequestExcuted.Invoke(this, result);
            }
        }

        #endregion

    }
}