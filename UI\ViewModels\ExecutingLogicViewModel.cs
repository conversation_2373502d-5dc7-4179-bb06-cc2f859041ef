﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities.Collectors;
using Common.UI.WPF.UI.ViewModels;
using Common.UI.WPF.UI.Views;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ControlzEx.Standard;
using Egor92.MvvmNavigation;
using Egor92.MvvmNavigation.Abstractions;
using GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers;
using GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers.RevitRequests;
using MaterialDesignThemes.Wpf;
using MEP.DuctTools.CoreLogic;
using MEP.DuctTools.UI.Models;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using UIFramework;

namespace MEP.DuctTools.UI.ViewModels
{
    internal partial class ExecutingLogicViewModel : ObservableObject, INavigatedToAware
    {

        #region Fields

        NavigationManager _navigationManager;
        Action _closeForm;
        BecaActivityLoggerData _taskLogger;
        RevitRequestMaker _rvtRequestMaker;

        [ObservableProperty]
        string _logs;

        [ObservableProperty]
        int _progressValue;

        UIDocument _uiRvtDoc;
        #endregion

        #region Properties



        #endregion

        #region Constructors

        public ExecutingLogicViewModel(UIDocument uiRvtDoc, NavigationManager navigationManager, /*SnackbarMessageQueue messageQueue, */RevitRequestMaker rvtRequestMaker, BecaActivityLoggerData taskLogger, Action closeForm)
        {
            _rvtRequestMaker = rvtRequestMaker;
            _taskLogger = taskLogger;
            _uiRvtDoc = uiRvtDoc;
            _navigationManager = navigationManager;
            _closeForm = closeForm;
        }

        #endregion

        #region Methods

        #region Rely Commands

        [RelayCommand]
        public void Exit()
        {
            _closeForm();
        }

        [RelayCommand]
        public void ExportLogs()
        {
            var saveFileDialog = new System.Windows.Forms.SaveFileDialog()
            {
                OverwritePrompt = false,
                DefaultExt = "txt",
                RestoreDirectory = true,
                Title = "Select or create new file.",
                InitialDirectory = Environment.GetFolderPath(Environment.SpecialFolder.Desktop),
                FileName = "Duct Morpher Logs"
            };

            if (saveFileDialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                File.WriteAllText(saveFileDialog.FileName, Logs);
            }

        }

        #endregion

        public void OnNavigatedTo(object arg)
        {
            // Retrieve the process from the static field instead of the parameter
            var ductsMorpherProcess = DuctMorpherStarterViewModel.CurrentProcess;

            if (ductsMorpherProcess == null)
            {
                ReportLogs("Couldn't get Duct Morpher Process ...", LogType.Error);
                return;
            }

            // Clear the static field after retrieving it
            DuctMorpherStarterViewModel.CurrentProcess = null;

            ReportLogs("Duct Morpher Started ...", LogType.Information);

            DuctMorpherMainLogic logic = new DuctMorpherMainLogic(_uiRvtDoc, ductsMorpherProcess, ReportLogs, ReportProgress);

            DuctMorpherMainLogicRevitRequest revitRqst = new DuctMorpherMainLogicRevitRequest(logic);
            revitRqst.onRequestExcuted += RevitRqst_onRequestExcuted;


            _rvtRequestMaker.MakeRequest(revitRqst);

        }

        private void RevitRqst_onRequestExcuted(GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers.RevitRequest request, bool isSuccessfulExcution)
        {
            if (isSuccessfulExcution)
            {
                ReportLogs("*************************************Duct Morpher Ended Successfully*************************************", LogType.Information);
            }
            else
            {
                ReportLogs("*************************************Duct Morpher failed*************************************", LogType.Error);
            }
        }

        void ReportProgress(int value)
        {
            ProgressValue = value;
            System.Windows.Forms.Application.DoEvents();
        }

        void ReportLogs(string message, LogType logtype)
        {
            Logs += Environment.NewLine + message;
            _taskLogger.Log(message, logtype);
            System.Windows.Forms.Application.DoEvents();
        }

        #endregion

    }
}