﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using BecaRevitUtilities.ElementUtilities;
using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels
{
    internal abstract class BaseDuctElementViewModel
    {

        #region Fields

        protected Element _rvtElement;
        protected string _elementId;
        protected ConnectorProfileType _ductShape;
        protected BuiltInCategory _category;
        protected string _familyName;
        protected string _typeName;
        protected string _size;

        protected bool _isLocked;

        #endregion

        #region Properties

        public string ElementId { get => _elementId; }
        public BuiltInCategory Category { get => _category; }
        public string FamilyName { get => _familyName; }
        public string TypeName { get => _typeName; }
        public bool IsLocked { get => _isLocked; }
        internal Element RvtElement { get => _rvtElement; }
        public ConnectorProfileType DuctShape { get => _ductShape; }
        public string Size { get => _size; }

        #endregion

        #region Constructors

        protected BaseDuctElementViewModel(Element rvtElement)
        {
            _rvtElement = rvtElement;

            _elementId = _rvtElement.Id.ToString();
            _category = (BuiltInCategory)_rvtElement.Category.Id.IntegerValue;
            _familyName = ElementUtility.GetFamilyName(_rvtElement);
            _typeName = _rvtElement.Name;
            _isLocked = WorksharingUtils.GetCheckoutStatus(rvtElement.Document, rvtElement.Id) == CheckoutStatus.OwnedByOtherUser;

        }



        #endregion

        #region Methods

        public abstract bool ChangeType(DuctType newType);

        #endregion

    }
}