﻿<UserControl
    x:Class="MEP.DuctTools.UI.Views.DuctMorpherStarterView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.DuctTools.UI.Views"
    xmlns:mah="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
    </UserControl.Resources>


    <DockPanel>

        <materialDesign:ColorZone
            Padding="15"
            materialDesign:ElevationAssist.Elevation="Dp4"
            Background="{StaticResource BecaTealBrush}"
            DockPanel.Dock="Top"
            Foreground="White"
            Mode="Custom">

            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                FontSize="22"
                FontWeight="Bold"
                Text="Duct Morpher" />

        </materialDesign:ColorZone>

        <Grid>

            <Grid.RowDefinitions>
                <RowDefinition Height="Auto" />
                <RowDefinition Height="1*" />
                <RowDefinition Height="Auto" />
                <RowDefinition Height="AUTO" />
                <RowDefinition Height="AUTO" />
            </Grid.RowDefinitions>

            <StackPanel
                Grid.Row="0"
                Margin="10,10,10,5"
                Orientation="Horizontal">
                <ComboBox
                    Width="200"
                    materialDesign:HintAssist.Hint="Elements Selection Method"
                    DisplayMemberPath="Key"
                    ItemsSource="{Binding ElementsSelectorMethods}"
                    SelectedValue="{Binding ElementSelectorMethod}"
                    SelectedValuePath="Value" />

                <Button
                    Width="100"
                    Height="30"
                    Margin="30,0,0,0"
                    materialDesign:ButtonAssist.CornerRadius="9"
                    Background="{StaticResource BecaYellowBrush}"
                    BorderBrush="{StaticResource BecaYellowBrush}"
                    Command="{Binding SelectCommand}"
                    Content="Select"
                    FontWeight="Bold"
                    Foreground="Black" />

                <Button
                    Width="100"
                    Height="30"
                    Margin="15,0,0,0"
                    materialDesign:ButtonAssist.CornerRadius="9"
                    Background="{StaticResource BecaYellowBrush}"
                    BorderBrush="{StaticResource BecaYellowBrush}"
                    Command="{Binding AppendCommand}"
                    Content="Append"
                    FontWeight="Bold"
                    Foreground="Black" />

            </StackPanel>


            <DataGrid
                Grid.Row="1"
                MinHeight="150"
                MaxHeight="340"
                Margin="10,5,10,5"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                AutoGenerateColumns="False"
                CanUserAddRows="False"
                EnableColumnVirtualization="True"
                EnableRowVirtualization="True"
                IsReadOnly="True"
                ItemsSource="{Binding SelectedElementsView}"
                SelectionMode="Single"
                SelectionUnit="FullRow"
                VerticalScrollBarVisibility="Auto"
                VirtualizingPanel.IsVirtualizingWhenGrouping="True">
                <DataGrid.Resources>
                    <Style BasedOn="{StaticResource {x:Type DataGridColumnHeader}}" TargetType="{x:Type DataGridColumnHeader}">
                        <Setter Property="Foreground" Value="{StaticResource BecaYellowBrush}" />
                        <Setter Property="FontSize" Value="12" />
                        <Setter Property="FontWeight" Value="Bold" />
                        <Setter Property="FontFamily" Value="Montserrat" />
                    </Style>
                    <Style BasedOn="{StaticResource {x:Type DataGridRow}}" TargetType="{x:Type DataGridRow}">
                        <Setter Property="Foreground" Value="Black" />
                        <Setter Property="FontSize" Value="10" />
                        <Setter Property="FontWeight" Value="SemiBold" />
                        <Setter Property="FontFamily" Value="Montserrat" />
                    </Style>
                    <SolidColorBrush x:Key="{x:Static SystemColors.HighlightBrushKey}" Color="{StaticResource BecaFuschia}" />
                </DataGrid.Resources>

                <DataGrid.GroupStyle>
                    <GroupStyle>
                        <GroupStyle.ContainerStyle>
                            <Style TargetType="GroupItem">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="{x:Type GroupItem}">

                                            <Expander
                                                x:Name="exp"
                                                Background="White"
                                                Foreground="Black"
                                                IsExpanded="True">
                                                <Expander.Header>
                                                    <Grid VerticalAlignment="Top">

                                                        <Grid.ColumnDefinitions>
                                                            <ColumnDefinition Width="1*" />
                                                            <ColumnDefinition Width="auto" />
                                                        </Grid.ColumnDefinitions>

                                                        <TextBlock
                                                            Grid.Column="0"
                                                            Margin="-2,0,0,0"
                                                            Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                            Text="{Binding Name}" />

                                                        <!--<Button
                                                            Grid.Column="1"
                                                            Width="25"
                                                            Height="25"
                                                            Margin="5"
                                                            Background="{StaticResource BecaBlueBrush}"
                                                            BorderBrush="{StaticResource BecaBlueBrush}"
                                                            Command="{Binding DataContext.ChangeApplyServerStatesGrpCommand, ElementName=userControl}"
                                                            CommandParameter="{Binding .}"
                                                            Style="{StaticResource MaterialDesignFloatingActionMiniLightButton}"
                                                            ToolTip="Accept/Reject Server Changes for whole group">
                                                            <materialDesign:PackIcon
                                                                Width="20"
                                                                Height="20"
                                                                Foreground="White"
                                                                Kind="SwapHorizontal" />
                                                        </Button>-->


                                                    </Grid>
                                                </Expander.Header>
                                                <ItemsPresenter />
                                            </Expander>

                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </GroupStyle.ContainerStyle>
                    </GroupStyle>
                </DataGrid.GroupStyle>

                <DataGrid.Columns>

                    <DataGridTextColumn
                        MaxWidth="220"
                        Binding="{Binding ElementId}"
                        Header="Element Id"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style>
                                <Setter Property="TextBlock.TextWrapping" Value="Wrap" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn
                        MaxWidth="220"
                        Binding="{Binding DuctShape}"
                        Header="Duct Shape"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style>
                                <Setter Property="TextBlock.TextWrapping" Value="Wrap" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>



                    <DataGridTextColumn
                        MaxWidth="220"
                        Binding="{Binding FamilyName}"
                        Header="Family Name"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style>
                                <Setter Property="TextBlock.TextWrapping" Value="Wrap" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn
                        MaxWidth="220"
                        Binding="{Binding TypeName}"
                        Header="Type Name"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style>
                                <Setter Property="TextBlock.TextWrapping" Value="Wrap" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridTextColumn
                        MaxWidth="150"
                        Binding="{Binding Size}"
                        Header="Size (cm)"
                        IsReadOnly="True">
                        <DataGridTextColumn.ElementStyle>
                            <Style>
                                <Setter Property="TextBlock.TextWrapping" Value="Wrap" />
                            </Style>
                        </DataGridTextColumn.ElementStyle>
                    </DataGridTextColumn>

                    <DataGridCheckBoxColumn
                        MaxWidth="75"
                        Binding="{Binding IsLocked, Mode=OneWay}"
                        Header="Is Locked" />

                </DataGrid.Columns>

            </DataGrid>


            <CheckBox
                Grid.Row="2"
                Margin="10,0,10,0"
                Content="Force Changes"
                FontWeight="Bold"
                IsChecked="{Binding IsForcingFittingChanges}" />

            <GroupBox
                Grid.Row="3"
                Margin="10"
                materialDesign:ColorZoneAssist.Background="{StaticResource BecaYellowBrush}"
                materialDesign:ColorZoneAssist.Foreground="White"
                materialDesign:ColorZoneAssist.Mode="Custom"
                Header="New Duct Types"
                Style="{StaticResource MaterialDesignGroupBox}">

                <StackPanel Orientation="Vertical">

                    <StackPanel Orientation="Horizontal">

                        <materialDesign:PackIcon
                            Width="30"
                            Height="30"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Foreground="{StaticResource BecaTealBrush}"
                            Kind="Rectangle" />

                        <ComboBox
                            MinWidth="200"
                            MaxWidth="600"
                            Margin="10,5,5,10"
                            HorizontalAlignment="Left"
                            materialDesign:HintAssist.Hint="New Rectangle Duct Type"
                            ItemsSource="{Binding RvtRectangleDuctTypes}"
                            SelectedItem="{Binding SelectedRectangleDuctType}"
                            Style="{StaticResource MaterialDesignFloatingHintComboBox}">

                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Name}" TextWrapping="Wrap" />
                                </DataTemplate>
                            </ComboBox.ItemTemplate>

                        </ComboBox>

                    </StackPanel>

                    <StackPanel Orientation="Horizontal">

                        <materialDesign:PackIcon
                            Width="30"
                            Height="30"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Foreground="{StaticResource BecaTealBrush}"
                            Kind="ShapeOvalPlus" />

                        <ComboBox
                            MinWidth="200"
                            MaxWidth="600"
                            Margin="10,5,5,10"
                            HorizontalAlignment="Left"
                            materialDesign:HintAssist.Hint="New Oval Duct Type"
                            ItemsSource="{Binding RvtOvalDuctTypes}"
                            SelectedItem="{Binding SelectedOvalDuctType}"
                            Style="{StaticResource MaterialDesignFloatingHintComboBox}">

                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Name}" TextWrapping="Wrap" />
                                </DataTemplate>
                            </ComboBox.ItemTemplate>

                        </ComboBox>

                    </StackPanel>



                    <StackPanel Orientation="Horizontal">

                        <materialDesign:PackIcon
                            Width="30"
                            Height="30"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Center"
                            Foreground="{StaticResource BecaTealBrush}"
                            Kind="CircleMultiple" />

                        <ComboBox
                            MinWidth="200"
                            MaxWidth="600"
                            Margin="10,5,5,10"
                            HorizontalAlignment="Left"
                            materialDesign:HintAssist.Hint="New Round Duct Type"
                            ItemsSource="{Binding RvtRoundDuctTypes}"
                            SelectedItem="{Binding SelectedRoundDuctType}"
                            Style="{StaticResource MaterialDesignFloatingHintComboBox}">

                            <ComboBox.ItemTemplate>
                                <DataTemplate>
                                    <TextBlock Text="{Binding Name}" TextWrapping="Wrap" />
                                </DataTemplate>
                            </ComboBox.ItemTemplate>

                        </ComboBox>

                    </StackPanel>

                </StackPanel>

            </GroupBox>




            <StackPanel
                Grid.Row="4"
                Margin="10,5,5,10"
                HorizontalAlignment="Center"
                Orientation="Horizontal">

                <Button
                    Width="120"
                    Height="45"
                    Margin="10,0,0,0"
                    materialDesign:ButtonAssist.CornerRadius="22"
                    Background="{StaticResource BecaFuschiaBrush}"
                    BorderBrush="{StaticResource BecaFuschiaBrush}"
                    Command="{Binding ExitCommand}"
                    Content="Exit"
                    FontSize="18"
                    Foreground="#ffffff" />

                <Button
                    Width="120"
                    Height="45"
                    Margin="10,0,0,0"
                    materialDesign:ButtonAssist.CornerRadius="22"
                    Background="{StaticResource BecaTealBrush}"
                    Command="{Binding RunCommand}"
                    Content="Run"
                    FontSize="18"
                    Foreground="#ffffff" />

            </StackPanel>

        </Grid>


    </DockPanel>
</UserControl>
