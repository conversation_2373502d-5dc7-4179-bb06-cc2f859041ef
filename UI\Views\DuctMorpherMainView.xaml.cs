﻿using Egor92.MvvmNavigation;
using Egor92.MvvmNavigation.Abstractions;
using GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers;
using MEP.DuctTools.UI.ViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace MEP.DuctTools.UI.Views
{
    /// <summary>
    /// Interaction logic for TransmittalReportExporterWindow.xaml
    /// </summary>
    public partial class DuctMorpherMainView : Window
    {

        static DuctMorpherMainView _mainWindow;

        public DuctMorpherMainView()
        {
            InitializeComponent();
        }


        public static void RunForm(Autodesk.Revit.UI.UIDocument uidoc, BecaActivityLogger.CoreLogic.Data.BecaActivityLoggerData _taskLogger)
        {
            if (_mainWindow == null)
            {

                var rvtRequestMaker = new RevitRequestMaker(_taskLogger);

                var mainWindowViewModel = new DuctMorpherMainViewModel(_taskLogger);

                _mainWindow = new DuctMorpherMainView() { DataContext = mainWindowViewModel };

                var navigationManager = new NavigationManager(_mainWindow.MainContent);

                navigationManager.Register<ExecutingLogicView>(nameof(ExecutingLogicView), () =>
                                new ExecutingLogicViewModel(uidoc, navigationManager, rvtRequestMaker, _taskLogger, () => _mainWindow.Close()));

                navigationManager.Register<DuctMorpherStarterView>(nameof(DuctMorpherStarterView),
                    () => new DuctMorpherStarterViewModel(uidoc, navigationManager, _taskLogger,
                    () => _mainWindow.Close(),
                    () => _mainWindow.Visibility = System.Windows.Visibility.Collapsed,
                    () => _mainWindow.Visibility = System.Windows.Visibility.Visible));

                navigationManager.Navigate(nameof(DuctMorpherStarterView));

                _mainWindow.Show();
            }
            else
            {
                _mainWindow.Show();
            }
        }

        private void Window_Closed(object sender, EventArgs e)
        {
            _mainWindow = null;
        }
    }
}
