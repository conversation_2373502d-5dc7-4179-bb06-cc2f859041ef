﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.DB.Plumbing;
using BecaRevitUtilities.ElementUtilities;
using BecaRevitUtilities.MEPUtilities.MEPElementsUtilities;
using Common.Utilities.Numbers;
using CommunityToolkit.Mvvm.ComponentModel;
using MEP.DuctTools.UI.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
//using System.Web.UI.WebControls;

namespace MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels
{
    [ObservableObject]
    internal partial class DuctElementViewModel : BaseDuctElementViewModel
    {

        #region Fields

        List<FittingTypeChanger> _fittingsToChangeType;


        #endregion

        #region Properties

        internal List<FittingTypeChanger> FittingsToChangeType { get => _fittingsToChangeType; }

        #endregion

        #region Constructors

        public DuctElementViewModel(Duct rvtElement) : base(rvtElement)
        {
            _fittingsToChangeType = new List<FittingTypeChanger>();
            _ductShape = rvtElement.DuctType.Shape;
            switch (_ductShape)
            {
                case ConnectorProfileType.Invalid:
                default:
                    _size = string.Empty;
                    break;
                case ConnectorProfileType.Round:
#if TargetYear2020
                    _size = UnitUtils.ConvertFromInternalUnits(rvtElement.Diameter, DisplayUnitType.DUT_CENTIMETERS).ToString();
#else
                    _size = UnitUtils.ConvertFromInternalUnits(rvtElement.Diameter, UnitTypeId.Centimeters).ToString();
#endif
                    break;
                case ConnectorProfileType.Rectangular:
                case ConnectorProfileType.Oval:

#if TargetYear2020
                    _size = $"{UnitUtils.ConvertFromInternalUnits(rvtElement.Width, DisplayUnitType.DUT_CENTIMETERS)} X {UnitUtils.ConvertFromInternalUnits(rvtElement.Height, DisplayUnitType.DUT_CENTIMETERS)}";
#else
                    _size = $"{UnitUtils.ConvertFromInternalUnits(rvtElement.Width, UnitTypeId.Centimeters)} X {UnitUtils.ConvertFromInternalUnits(rvtElement.Height, UnitTypeId.Centimeters)}";
#endif
                    break;
            }


        }


        #endregion

        #region Methods

        public override bool ChangeType(DuctType newType)
        {
            Duct duct = _rvtElement as Duct;
            duct.ChangeTypeId(newType.Id);

            foreach (Connector r in duct.ConnectorManager.Connectors)
            {
                foreach (Connector connector in r.AllRefs)
                {
                    if (connector.Owner.Category.Id.IntegerValue == (int)BuiltInCategory.OST_PipeFitting)
                    {
                        var fs = connector.Owner as FamilyInstance;
                        var mecMepModel = fs.MEPModel as MechanicalFitting;

                        var newTypeFittingId = MEPElementUtility.GetSuitableFitting(newType, mecMepModel.PartType, duct.Diameter);

                        if (connector.Owner.GetTypeId().IntegerValue != newTypeFittingId.IntegerValue)
                        {
                            FittingsToChangeType.Add(new FittingTypeChanger(connector.Owner, newTypeFittingId));
                        }

                    }

                }
            }


            return true;
        }




        #endregion

    }
}