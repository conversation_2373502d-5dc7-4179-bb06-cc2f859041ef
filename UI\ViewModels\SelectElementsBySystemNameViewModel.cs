﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities.Collectors;
using Common.UI.WPF.UI.ViewModels;
using Common.UI.WPF.UI.Views;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ControlzEx.Standard;
using Egor92.MvvmNavigation;
using Egor92.MvvmNavigation.Abstractions;
using MaterialDesignThemes.Wpf;
using MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Data;
using UIFramework;

namespace MEP.DuctTools.UI.ViewModels
{
    [ObservableObject]
    internal partial class SelectElementsBySystemNameViewModel
    {

        #region Fields

        private BecaActivityLoggerData taskLogger;
        Document _rvtDoc;
        NavigationManager _navigationManager;

        [ObservableProperty]
        string _filterTxt;


        [ObservableProperty]
        ListCollectionView _rvtMechincalSystems;

        #endregion

        #region Properties


        #endregion

        #region Constructors

        public SelectElementsBySystemNameViewModel(Document rvtDoc, NavigationManager navigationManager, /*SnackbarMessageQueue messageQueue, */BecaActivityLoggerData taskLogger)
        {
            this.taskLogger = taskLogger;
            _rvtDoc = rvtDoc;
            _navigationManager = navigationManager;
            InitializeData();
        }

        #endregion

        #region Methods

        #region Partial Methods

        partial void OnFilterTxtChanged(string value)
        {
            _rvtMechincalSystems.Filter = DoesCollectionContainName;
        }

        #endregion

        #region Helper

        private bool DoesCollectionContainName(object objEsEle)
        {
            SelectableGroupedMechincalSystemViewModel esEle = objEsEle as SelectableGroupedMechincalSystemViewModel;
            esEle.MechanicalSystemsView.Filter = DoesSystemCollectionContainName;
            return true;
        }

        private bool DoesSystemCollectionContainName(object objEsEle)
        {
            SelectableMechanicalSystemViewModel esEle = objEsEle as SelectableMechanicalSystemViewModel;
            return esEle.ViewModel.Name.ToLower().Contains(FilterTxt.ToLower()) ;
        }

        void InitializeData()
        {
            var ductSystems = new FilteredElementCollector(_rvtDoc).OfCategory(BuiltInCategory.OST_DuctSystem).OfClass(typeof(MEPSystem)).Cast<MechanicalSystem>().GroupBy(x => x.SystemType);
            List<SelectableGroupedMechincalSystemViewModel> groupedDuctSystems = new List<SelectableGroupedMechincalSystemViewModel>(ductSystems.Count());
            foreach (var item in ductSystems)
            {
                var systems = item.Select(x =>
                new SelectableMechanicalSystemViewModel(new MechanicalSystemViewModel() { Name = x.Name })).ToList();

                groupedDuctSystems.Add(new SelectableGroupedMechincalSystemViewModel(
                    new GroupedMechincalSystemViewModel() { GroupName = item.Key.ToString(), MechanicalSystems = systems }));
            }

            RvtMechincalSystems = new ListCollectionView(groupedDuctSystems);

        }

        #endregion


        #endregion

    }
}