﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels
{
    internal class GroupedMechincalSystemViewModel
    {

        #region Fields


        string _groupName;
        List<SelectableMechanicalSystemViewModel> _mechanicalSystems;


        #endregion

        #region Properties

        public string GroupName { get => _groupName; set => _groupName = value; }
        public List<SelectableMechanicalSystemViewModel> MechanicalSystems { get => _mechanicalSystems; set => _mechanicalSystems = value; }


        #endregion

        #region Constructors



        #endregion

        #region Methods



        #endregion

    }
}