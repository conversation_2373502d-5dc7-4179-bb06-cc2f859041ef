﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI.Selection;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers
{
    /// <summary>
    ///   A class with methods to execute requests made by the dialog user.
    /// </summary>
    /// 
    public class RevitRequestHandler : IExternalEventHandler
    {

        #region Fields

        private readonly object _lock;
        public ExternalEvent _revitEvent;


        internal static List<RevitRequest> Requests;

        // The value of the latest request made by the modeless form 
        private BecaActivityLoggerData logger;

        #endregion

        #region Properties



        #endregion

        #region Constructors

        public RevitRequestHandler()
        {
            Requests = new List<RevitRequest>();
            _lock = new object();
        }

        public RevitRequestHandler(BecaActivityLoggerData logger) : this()
        {
            this.logger = logger;
        }

        #endregion

        #region Methods

        #region IExternalEventHandler Methods

        /// <summary>
        ///   A method to identify this External Event Handler
        /// </summary>
        public String GetName()
        {
            return "Element sharing Revit Handler";
        }

        public async void Execute(UIApplication uiapp)
        {
            lock (_lock)
            {

            }

            foreach (var r in Requests.OrderBy(e => e.RevitRequestId))
            {
                r.ExcuteRequest(uiapp, logger);
            }
            Requests.Clear();
        }

        #endregion


        #endregion

    }
}
