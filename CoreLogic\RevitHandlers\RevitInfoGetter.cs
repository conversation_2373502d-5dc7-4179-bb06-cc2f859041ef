﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using Autodesk.Revit.UI;
using BecaRevitUtilities;
using BecaRevitUtilities.Collectors;
using BecaRevitUtilities.ElementUtilities;
using ControlzEx.Standard;
using GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GEN.ElementSharing.Enhanced.CoreLogic
{
    /// <summary>
    /// class responsible for every action needed from revit.
    /// </summary>
    internal class RevitInfoGetter
    {

        #region Fields

        UIApplication _uiApp;


        #endregion

        #region Properties

        public Document RvtDocument { get => UiApp.ActiveUIDocument.Document; }
        public UIApplication UiApp { get => _uiApp; }

        #endregion

        #region Constructors

        internal RevitInfoGetter(UIApplication uiApp)
        {
            _uiApp = uiApp;
        }

        #endregion

        #region Methods

        public string GetAutodeskUserName()
        {
            return _uiApp.Application.Username;
        }

        internal List<Element> GetAllScopeBoxes()
        {
            return ElementCollectorUtility.GetAllScopeBoxes(_uiApp.ActiveUIDocument.Document).ToList();
        }

        internal IEnumerable<Space> GetAllSpaces()
        {
            return ElementCollectorUtility.GetAllSpaces(_uiApp.ActiveUIDocument.Document);
        }

        internal List<Workset> GetAllWorksets()
        {
            return WorksetUtility.GetWorksets(_uiApp.ActiveUIDocument.Document).ToList();
        }

        internal IOrderedEnumerable<Level> GetOrderedLevels()
        {
            return LevelUtility.GetSortedLevels_AllDocLevels(_uiApp.ActiveUIDocument.Document);
        }

        internal FilteredElementCollector FilterElement(LogicalAndFilter filters)
        {
            return new FilteredElementCollector(_uiApp.ActiveUIDocument.Document).WhereElementIsNotElementType().WherePasses(filters);
        }

        internal string GetProjectName()
        {
            return _uiApp.ActiveUIDocument.Document.ProjectInformation.Name;
        }

        internal string GetProjectNumber()
        {
            return _uiApp.ActiveUIDocument.Document.ProjectInformation.Number;
        }

        internal List<Document> GetLinkedModels()
        {
            return LinkedFilesUtility.GetRVTLinks(_uiApp.ActiveUIDocument.Document);
        }

        internal List<RevitLinkInstance> GetRVTLinksAsRevitLinkInstance()
        {
            return LinkedFilesUtility.GetRVTLinksAsRevitLinkInstance(_uiApp.ActiveUIDocument.Document);
        }


        internal string GetActiveDocumentTitle()
        {
            return _uiApp.ActiveUIDocument.Document.Title;
        }

        internal Guid GetActiveDocumentGUID()
        {
            if (_uiApp.ActiveUIDocument.Document.IsModelInCloud)
            {
                return _uiApp.ActiveUIDocument.Document.WorksharingCentralGUID;
            }
            else
            {
                throw new NotImplementedException("no local docs available yet.");
            }
        }


        internal Category GetCategoryById(int id)
        {
            return Category.GetCategory(_uiApp.ActiveUIDocument.Document, (BuiltInCategory)id);
        }


        #endregion

    }
}