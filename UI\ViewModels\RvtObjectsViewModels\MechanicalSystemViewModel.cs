﻿using CommunityToolkit.Mvvm.ComponentModel;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels
{
    [ObservableObject]
    internal partial class MechanicalSystemViewModel
    {

        #region Fields

        string _name;


        #endregion

        #region Properties

        public string Name { get => _name; set => _name = value; }

        #endregion

        #region Constructors



        #endregion

        #region Methods



        #endregion

    }
}