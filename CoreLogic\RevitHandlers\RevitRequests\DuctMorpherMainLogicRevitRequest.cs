﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities.RevitViewsUtilities;
using BecaTransactionsNamesManager;
using CommunityToolkit.Mvvm.DependencyInjection;
using MEP.DuctTools.CoreLogic;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers.RevitRequests
{
    internal class DuctMorpherMainLogicRevitRequest : RevitRequest
    {

        #region Fields

        private DuctMorpherMainLogic _logic;


        #endregion

        #region Properties



        #endregion

        #region Constructors


        public DuctMorpherMainLogicRevitRequest(DuctMorpherMainLogic logic) : base(RevitRequestId.DuctMorpherMainLogic)
        {
            _logic = logic;
        }

        #endregion

        #region Methods

        protected override bool Execute(UIApplication uiapp, BecaActivityLoggerData logger)
        {
            return _logic.Run();
        }

        #endregion

    }
}