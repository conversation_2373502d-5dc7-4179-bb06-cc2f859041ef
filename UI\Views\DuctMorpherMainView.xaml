﻿<Window
    x:Class="MEP.DuctTools.UI.Views.DuctMorpherMainView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:extensions="clr-namespace:Common.UI.WPF.Extensions;assembly=Common.UI.WPF"
    xmlns:local="clr-namespace:MEP.DuctTools.UI.Views"
    xmlns:mah="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:validationRules="clr-namespace:Common.UI.WPF.ValidationRules;assembly=Common.UI.WPF"
    xmlns:viewModels="clr-namespace:MEP.DuctTools.UI.ViewModels"
    Title="Ducts Morpher"
    Width="900"
    Height="800"
    Closed="Window_Closed"
    WindowStartupLocation="CenterScreen"
    mc:Ignorable="d">

    <Window.Resources>
        <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
    </Window.Resources>

    <materialDesign:DialogHost
        DialogTheme="Inherit"
        Identifier="RootDialog_DM">
        <Grid>


            <ScrollViewer
                HorizontalAlignment="Stretch"
                VerticalAlignment="Stretch"
                materialDesign:ScrollViewerAssist.IsAutoHideEnabled="True"
                VerticalScrollBarVisibility="Auto">

                <mah:ContentControlEx
                    x:Name="MainContent"
                    HorizontalAlignment="Stretch"
                    VerticalAlignment="Stretch" />

            </ScrollViewer>
        </Grid>


    </materialDesign:DialogHost>

</Window>
