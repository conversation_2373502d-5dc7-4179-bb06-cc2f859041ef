<Project Sdk="Microsoft.NET.Sdk">
	<PropertyGroup>
		<UseWPF>true</UseWPF>
		<Configurations>Debug R20;Debug R21;Debug R22;Debug R23;Debug R24;Debug R25;Release R20;Release R21;Release R22;Release R23;Release R24;Release R25;Debug R26;Release R26</Configurations>
	</PropertyGroup>
	<ItemGroup>
	  <Compile Remove="UI\ModelessRevitForm\**" />
	  <EmbeddedResource Remove="UI\ModelessRevitForm\**" />
	  <None Remove="UI\ModelessRevitForm\**" />
	  <Page Remove="UI\ModelessRevitForm\**" />
	</ItemGroup>
	<ItemGroup>
		<PackageReference Include="JetBrains.Annotations" Version="2024.2.0" />
		<PackageReference Include="MahApps.Metro" Version="2.4.10" />
		<PackageReference Include="MaterialDesignColors" Version="2.1.4" />
		<PackageReference Include="MaterialDesignThemes.MahApps" Version="0.3.0" />
		<PackageReference Include="MaterialDesignThemes.Wpf" Version="1.0.1" />
		<PackageReference Include="Microsoft.Bcl.AsyncInterfaces" Version="8.0.0" />
		<PackageReference Include="Microsoft.Office.Interop.Excel" Version="15.0.4795.1001" />
		<PackageReference Include="MvvmNavigation.Abstractions" Version="3.2.0" />
		<PackageReference Include="MvvmNavigation.Core" Version="3.2.0" />
		<PackageReference Include="MvvmNavigation.Wpf" Version="3.2.0" />
	</ItemGroup>
	<ItemGroup>
		<ProjectReference Include="..\..\COMMON\BecaActivityLogger\BecaActivityLogger.csproj" />
		<ProjectReference Include="..\..\COMMON\BecaCommand\BecaCommand.csproj" />
		<ProjectReference Include="..\..\COMMON\BecaRevitUtilities\BecaRevitUtilities.csproj" />
		<ProjectReference Include="..\..\COMMON\BecaTransactionsNames\BecaTransactionsNamesManager.csproj" />
		<ProjectReference Include="..\..\COMMON\BecaTrekaHandler\BecaTrekaHandler.csproj" />
		<ProjectReference Include="..\..\COMMON\Common.EnhancedADGV\Common.EnhancedADGV.csproj" />
		<ProjectReference Include="..\..\COMMON\Common.Extenstions\Common.Extenstions.csproj" />
		<ProjectReference Include="..\..\COMMON\Common.UI.WPF\Common.UI.WPF.csproj" />
		<ProjectReference Include="..\..\COMMON\Common.UI\Common.UI.csproj" />
		<ProjectReference Include="..\..\COMMON\Common.Utilities\Common.Utilities.csproj" />
	</ItemGroup>
	<ItemGroup>
		<Reference Include="Common.BecaLicense">
			<HintPath>..\..\3rdParties\BecaLicensing\Common.BecaLicense.dll</HintPath>
		</Reference>
	</ItemGroup>

</Project>