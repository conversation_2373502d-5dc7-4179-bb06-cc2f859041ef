﻿using Autodesk.Revit.UI;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace GEN.ElementSharing.Enhanced.CoreLogic.RevitHandlers
{
    internal class RevitRequestMaker
    {

        #region Fields

        private RevitRequestHandler _handler;
        private ExternalEvent _externalEvent;

        #endregion

        #region Properties



        #endregion

        #region Constructors

        public RevitRequestMaker(BecaActivityLogger.CoreLogic.Data.BecaActivityLoggerData logger)
        {
            // A new handler to handle request posting by the dialog
            _handler = new RevitRequestHandler(logger);

            // External Event for the dialog to use (to post requests)
            _externalEvent = ExternalEvent.Create(_handler);
        }

        #endregion

        #region Methods

        public void MakeRequest(RevitRequest request)
        {
            RevitRequestHandler.Requests.Add(request);
            _externalEvent.Raise();
        }

        #endregion

    }
}