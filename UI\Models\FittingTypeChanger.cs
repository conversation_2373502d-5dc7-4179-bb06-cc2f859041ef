﻿using Autodesk.Revit.DB;
using BecaActivityLogger.CoreLogic.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.DuctTools.UI.Models
{
    internal class FittingTypeChanger
    {

        #region Fields

        Element _fitting;
        ElementId _newTypeFittingId;

        #endregion

        #region Properties


        public Element Fitting { get => _fitting; }
        public ElementId NewTypeFittingId { get => _newTypeFittingId; }


        #endregion

        #region Constructors

        public FittingTypeChanger(Element fitting, ElementId newTypeId)
        {
            _fitting = fitting;
            _newTypeFittingId = newTypeId;
        }

        #endregion

        #region Methods

        public bool Run()
        {
            if (_fitting.IsValidObject)
            {
                if (_fitting.GetValidTypes().Any(x => x.IntegerValue == NewTypeFittingId?.IntegerValue))
                {
                    if (_fitting.ChangeTypeId(_newTypeFittingId) != ElementId.InvalidElementId)
                    {
                        return false;
                    }
                }
                else
                {
                    return false;
                }
            }

            return true;
        }

        #endregion

    }
}