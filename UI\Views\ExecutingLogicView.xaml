﻿<UserControl
    x:Class="MEP.DuctTools.UI.Views.ExecutingLogicView"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:MEP.DuctTools.UI.Views"
    xmlns:mah="clr-namespace:MahApps.Metro.Controls;assembly=MahApps.Metro"
    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d">

    <UserControl.Resources>
        <ResourceDictionary Source="pack://application:,,,/Common.UI.WPF;component/UI/Dictionaries/BecaMainDictionary.xaml" />
    </UserControl.Resources>


    <DockPanel>

        <materialDesign:ColorZone
            Padding="15"
            materialDesign:ElevationAssist.Elevation="Dp4"
            Background="{StaticResource BecaTealBrush}"
            DockPanel.Dock="Top"
            Foreground="White"
            Mode="Custom">

            <Grid>

                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="auto" />
                </Grid.ColumnDefinitions>

                <!--<Button
                    Grid.Column="0"
                    Command="{Binding PreviousCommand}"
                    Content="{materialDesign:PackIcon Kind=ArrowLeft,
                                                      Size=30}"
                    Foreground="{Binding RelativeSource={RelativeSource AncestorType={x:Type FrameworkElement}}, Path=(TextElement.Foreground)}"
                    Style="{StaticResource MaterialDesignToolButton}"
                    ToolTip="Previous" />-->

                <TextBlock
                    Grid.Column="1"
                    HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    FontSize="22"
                    FontWeight="Bold"
                    Text="Executing Logic" />

                <!--<Button
                    Grid.Column="2"
                    Command="{Binding NextCommand}"
                    Content="{materialDesign:PackIcon Kind=ArrowRight,
                                                      Size=30}"
                    Foreground="{Binding RelativeSource={RelativeSource AncestorType={x:Type FrameworkElement}}, Path=(TextElement.Foreground)}"
                    Style="{StaticResource MaterialDesignToolButton}"
                    ToolTip="Next" />-->

            </Grid>

        </materialDesign:ColorZone>
        <Grid DockPanel.Dock="Top">
            <ProgressBar
                x:Name="pbStatus"
                Height="20"
                HorizontalAlignment="Stretch"
                VerticalAlignment="Top"
                d:Value="80"
                Background="White"
                Foreground="{StaticResource BecaYellowBrush}"
                Value="{Binding ProgressValue}" />
            <TextBlock
                HorizontalAlignment="Center"
                VerticalAlignment="Center"
                d:Text="100%"
                FontWeight="Bold"
                Foreground="{StaticResource BecaFuschiaBrush}"
                Text="{Binding ElementName=pbStatus, Path=Value, StringFormat={}{0:0}%}" />

        </Grid>

        <Grid>

            <Grid.RowDefinitions>
                <RowDefinition Height="1*" />
                <RowDefinition Height="auto" />
            </Grid.RowDefinitions>

            <GroupBox
                Grid.Row="0"
                Margin="15"
                materialDesign:ColorZoneAssist.Background="{StaticResource BecaYellowBrush}"
                materialDesign:ColorZoneAssist.Foreground="White"
                materialDesign:ColorZoneAssist.Mode="Custom"
                Header="Logs"
                Style="{StaticResource MaterialDesignGroupBox}">

                <Grid>

                    <TextBox
                        MaxHeight="540"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch"
                        SpellCheck.IsEnabled="False"
                        Style="{StaticResource MaterialDesignOutlinedTextBox}"
                        Text="{Binding Logs}"
                        TextChanged="TextBox_TextChanged"
                        TextWrapping="Wrap"
                        VerticalScrollBarVisibility="Auto" />

                    <Button
                        Width="25"
                        Height="25"
                        Margin="15"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Bottom"
                        Background="{StaticResource BecaTealBrush}"
                        BorderBrush="{StaticResource BecaTealBrush}"
                        Command="{Binding ExportLogsCommand}"
                        Style="{StaticResource MaterialDesignFloatingActionMiniLightButton}"
                        ToolTip="Export Logs to text file">
                        <materialDesign:PackIcon
                            HorizontalAlignment="Stretch"
                            VerticalAlignment="Stretch"
                            Foreground="White"
                            Kind="ExportVariant" />
                    </Button>
                </Grid>

            </GroupBox>

            <Button
                Grid.Row="1"
                Width="120"
                Height="45"
                Margin="0,5,0,10"
                materialDesign:ButtonAssist.CornerRadius="22"
                Background="{StaticResource BecaTealBrush}"
                Command="{Binding ExitCommand}"
                Content="Exit"
                FontSize="18"
                Foreground="#ffffff" />

        </Grid>


    </DockPanel>
</UserControl>
