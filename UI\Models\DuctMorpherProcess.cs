﻿using Autodesk.Revit.DB;
using Autodesk.Revit.DB.Mechanical;
using MEP.DuctTools.UI.ViewModels.RvtObjectsViewModels;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MEP.DuctTools.UI.Models
{
    internal class DuctMorpherProcess
    {

        #region Fields

        List<BaseDuctElementViewModel> _elementsToProcess;

        DuctType _targetRoundDuctType;
        DuctType _targetOvalDuctType;
        DuctType _targetRectangleDuctType;

        bool _isForcingFittingChanges;



        #endregion

        #region Properties


        internal List<BaseDuctElementViewModel> ElementsToProcess { get => _elementsToProcess; }
        public bool IsForcingFittingChanges { get => _isForcingFittingChanges; }
        public DuctType TargetRoundDuctType { get => _targetRoundDuctType; }
        public DuctType TargetOvalDuctType { get => _targetOvalDuctType; }
        public DuctType TargetRectangleDuctType { get => _targetRectangleDuctType; }


        #endregion

        #region Constructors

        public DuctMorpherProcess(List<BaseDuctElementViewModel> elementsToProcess, DuctType targetRoundDuctType, DuctType targetOvalDuctType, DuctType targetRectangleDuctType, bool isForcingFittingChanges)
        {
            _elementsToProcess = elementsToProcess;
            _targetRoundDuctType = targetRoundDuctType;
            _targetOvalDuctType = targetOvalDuctType;
            _targetRectangleDuctType = targetRectangleDuctType;
            _isForcingFittingChanges = isForcingFittingChanges;
        }

        #endregion

        #region Methods

        public DuctType GetDuctType(ConnectorProfileType profileType)
        {
            switch (profileType)
            {
                case ConnectorProfileType.Invalid:
                    return null;
                case ConnectorProfileType.Round:
                    return _targetRoundDuctType;
                case ConnectorProfileType.Rectangular:
                    return _targetRectangleDuctType;
                case ConnectorProfileType.Oval:
                    return _targetOvalDuctType;
                default:
                    return null;

            }
        }

        #endregion

    }
}