﻿using Autodesk.Revit.DB;
using Autodesk.Revit.UI;
using BecaActivityLogger.CoreLogic.Data;
using BecaRevitUtilities.Collectors;
using Common.UI.WPF.UI.ViewModels;
using Common.UI.WPF.UI.Views;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using ControlzEx.Standard;
using Egor92.MvvmNavigation;
using MaterialDesignThemes.Wpf;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using UIFramework;

namespace MEP.DuctTools.UI.ViewModels
{
    public partial class DuctMorpherMainViewModel : ObservableObject
    {

        #region Fields

        private BecaActivityLoggerData taskLogger;

        #endregion

        #region Properties

        #endregion

        #region Constructors

        public DuctMorpherMainViewModel(BecaActivityLoggerData taskLogger)
        {
            this.taskLogger = taskLogger;
        }

        #endregion

        #region Methods




        #endregion

    }
}